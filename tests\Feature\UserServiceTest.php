<?php

namespace Tests\Feature;

use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class UserServiceTest extends TestCase
{
    use RefreshDatabase;

    protected UserService $userService;

    public function setUp(): void
    {
        parent::setUp();
        $this->userService = $this->app->make(UserService::class);

        // Create roles
        foreach (RoleEnum::cases() as $role) {
            Role::create(['name' => $role->value]);
        }
    }

    /** @test */
    public function it_can_create_a_user_with_valid_data()
    {
        $userData = [
            'name' => 'Test User',
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'status' => UserStatus::Active,
            'role' => RoleEnum::ADMIN->value,
        ];

        $user = $this->userService->create($userData);

        $this->assertDatabaseHas('users', [
            'name' => 'Test User',
            'username' => 'testuser',
            'email' => '<EMAIL>',
        ]);

        $this->assertTrue($user->hasRole(RoleEnum::ADMIN->value));
    }

    /** @test */
    public function it_applies_business_logic_rules()
    {
        // Create 5 admin users first
        for ($i = 1; $i <= 5; $i++) {
            $user = User::factory()->create([
                'name' => "Admin $i",
                'username' => "admin$i",
                'email' => "admin$<EMAIL>",
            ]);
            $user->assignRole(RoleEnum::ADMIN->value);
        }

        $userData = [
            'name' => 'Test Admin',
            'username' => 'testadmin',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'status' => UserStatus::Active,
            'role' => RoleEnum::ADMIN->value,
        ];

        $this->expectException(\App\Exceptions\BusinessLogicException::class);
        $this->expectExceptionMessage('Maksimal jumlah pengguna admin (5) telah tercapai.');

        $this->userService->create($userData);
    }
}
