<?php

namespace App\Contracts\Interfaces;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;

interface UserRepositoryInterface
{
    /**
     * Get all users with optional filtering
     */
    public function getAll(array $filters = []): Collection;

    /**
     * Find a user by ID
     *
     * @throws \App\Exceptions\NotFoundException
     */
    public function findById(int $id): User;

    /**
     * Get users by role
     */
    public function getUsersByRole(string $roleName): Collection;



    /**
     * Create a new user
     */
    public function create(array $data): User;



    /**
     * Update an existing user
     */
    public function update(int $id, array $data): bool;



    /**
     * Delete a user
     */
    public function delete(int $id): bool;





    /**
     * Count users by role
     */
    public function countUsersByRole(string $role): int;

    /**
     * Count active users by role
     */
    public function countActiveUsersByRole(string $role): int;

    /**
     * Change user status
     */
    public function changeStatus(int $id, bool $status): bool;

    /**
     * Verify user credentials
     */
    public function verifyCredentials(string $username, string $password): ?User;

    /**
     * Find user by email
     */
    public function findByEmail(string $email): ?User;

    /**
     * Find user by username
     */
    public function findByUsername(string $username): ?User;

    // Legacy method aliases for backward compatibility
    /**
     * @deprecated Use getAll() instead
     */
    public function getAllUsers(array $filters = []): Collection;

    /**
     * @deprecated Use findById() instead
     */
    public function getUserById(int $userId): User;

    /**
     * @deprecated Use create() instead
     */
    public function createUser(array $userDetails): User;

    /**
     * @deprecated Use update() instead
     */
    public function updateUser(int $userId, array $newDetails): bool;

    /**
     * @deprecated Use delete() instead
     */
    public function deleteUser(int $userId): bool;
}
