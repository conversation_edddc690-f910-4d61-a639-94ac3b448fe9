<?php

namespace App\Repositories;

use App\Contracts\Interfaces\UserRepositoryInterface;
use App\Enums\UserStatus;
use App\Exceptions\NotFoundException;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Hash;

class UserRepository implements UserRepositoryInterface
{
    protected User $model;

    public function __construct(User $model)
    {
        $this->model = $model;
    }

    /**
     * Get all users with optional filtering
     */
    public function getAll(array $filters = []): Collection
    {
        $query = $this->model->with('roles');

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['role'])) {
            $query->whereHas('roles', function ($q) use ($filters) {
                $q->where('name', $filters['role']);
            });
        }

        if (isset($filters['role_in']) && is_array($filters['role_in'])) {
            $query->whereHas('roles', function ($q) use ($filters) {
                $q->whereIn('name', $filters['role_in']);
            });
        }

        if (isset($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                    ->orWhere('email', 'like', $searchTerm)
                    ->orWhere('username', 'like', $searchTerm);
            });
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Find a user by ID
     */
    public function findById(int $id): User
    {
        $user = $this->model->with('roles')->find($id);

        if (!$user) {
            throw new NotFoundException("User dengan ID {$id} tidak ditemukan");
        }

        return $user;
    }

    /**
     * Get users by role
     */
    public function getUsersByRole(string $roleName): Collection
    {
        return $this->model->role($roleName)->get();
    }



    /**
     * Create a new user
     */
    public function create(array $data): User
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        $user = $this->model->create($data);

        // Assign role if provided
        if (isset($data['role'])) {
            $user->assignRole($data['role']);
        }

        return $user->load('roles');
    }



    /**
     * Update an existing user
     */
    public function update(int $id, array $data): bool
    {
        $user = $this->findById($id);

        // Hash password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        } else {
            unset($data['password']);
        }

        // Handle role update
        if (isset($data['role'])) {
            $user->syncRoles([$data['role']]);
            unset($data['role']);
        }

        return $user->update($data);
    }



    /**
     * Delete a user
     */
    public function delete(int $id): bool
    {
        $user = $this->findById($id);
        return $user->delete();
    }





    /**
     * Count users by role
     */
    public function countUsersByRole(string $role): int
    {
        return $this->model->whereHas('roles', function ($q) use ($role) {
            $q->where('name', $role);
        })->count();
    }

    /**
     * Count active users by role
     */
    public function countActiveUsersByRole(string $role): int
    {
        return $this->model->whereHas('roles', function ($q) use ($role) {
            $q->where('name', $role);
        })->where('status', UserStatus::Active)->count();
    }

    /**
     * Change user status
     */
    public function changeStatus(int $id, bool $status): bool
    {
        $user = $this->findById($id);
        return $user->update([
            'status' => $status ? UserStatus::Active : UserStatus::Inactive
        ]);
    }

    /**
     * Verify user credentials
     */
    public function verifyCredentials(string $username, string $password): ?User
    {
        $user = $this->model->where(function ($query) use ($username) {
            $query->where('username', $username)
                ->orWhere('email', $username);
        })->first();

        if ($user && Hash::check($password, $user->password)) {
            return $user;
        }

        return null;
    }

    /**
     * Find user by email
     */
    public function findByEmail(string $email): ?User
    {
        return $this->model->where('email', $email)->first();
    }

    /**
     * Find user by username
     */
    public function findByUsername(string $username): ?User
    {
        return $this->model->where('username', $username)->first();
    }

    // Legacy method aliases for backward compatibility
    /**
     * @deprecated Use getAll() instead
     */
    public function getAllUsers(array $filters = []): Collection
    {
        return $this->getAll($filters);
    }

    /**
     * @deprecated Use findById() instead
     */
    public function getUserById(int $userId): User
    {
        return $this->findById($userId);
    }

    /**
     * @deprecated Use create() instead
     */
    public function createUser(array $userDetails): User
    {
        return $this->create($userDetails);
    }

    /**
     * @deprecated Use update() instead
     */
    public function updateUser(int $userId, array $newDetails): bool
    {
        return $this->update($userId, $newDetails);
    }

    /**
     * @deprecated Use delete() instead
     */
    public function deleteUser(int $userId): bool
    {
        return $this->delete($userId);
    }
}
